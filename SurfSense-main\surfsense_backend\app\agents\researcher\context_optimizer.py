"""
Context Optimization for RAG Systems

This module addresses the "Lost in the Middle" problem where language models
tend to focus on information at the beginning and end of the context window,
often ignoring relevant information in the middle.
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import math

logger = logging.getLogger(__name__)

try:
    from app.config.context_config import context_config
except ImportError:
    # Fallback if config is not available
    context_config = None


class DocumentPlacementStrategy(Enum):
    """Strategies for placing documents in context to optimize attention."""
    RELEVANCE_FIRST = "relevance_first"  # Most relevant first (current default)
    RELEVANCE_LAST = "relevance_last"    # Most relevant last
    SANDWICH = "sandwich"                # Most relevant at start and end
    INTERLEAVED = "interleaved"          # Alternate high/low relevance
    ATTENTION_OPTIMIZED = "attention_optimized"  # Based on attention patterns


@dataclass
class DocumentWithScore:
    """Document with relevance score and metadata."""
    document: Dict[str, Any]
    relevance_score: float
    token_count: int
    document_id: str
    title: str
    content_preview: str


class ContextOptimizer:
    """
    Optimizes document placement in context to mitigate "Lost in the Middle" problem.
    """
    
    def __init__(self, strategy: DocumentPlacementStrategy = DocumentPlacementStrategy.ATTENTION_OPTIMIZED, model_name: str = ""):
        self.strategy = strategy
        self.model_name = model_name

        # Load attention weights from config or use defaults
        if context_config and model_name:
            self.attention_weights = context_config.get_attention_weights_for_model(model_name)
        else:
            self.attention_weights = self._get_default_attention_weights()
    
    def _get_default_attention_weights(self) -> Dict[str, float]:
        """
        Get default attention weights for different positions in context.
        Based on research showing models pay more attention to start and end.
        """
        return {
            "start": 1.0,      # Beginning of context gets full attention
            "early": 0.8,      # Early positions get good attention
            "middle": 0.4,     # Middle positions get less attention (the problem!)
            "late": 0.7,       # Late positions get decent attention
            "end": 0.9         # End positions get high attention
        }
    
    def optimize_document_order(
        self, 
        documents: List[Dict[str, Any]], 
        available_tokens: int,
        query: str = ""
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Optimize document order and selection for maximum effectiveness.
        
        Args:
            documents: List of documents with scores
            available_tokens: Available token budget
            query: Original query for context
            
        Returns:
            Tuple of (optimized_documents, optimization_info)
        """
        if not documents:
            return [], {"strategy": self.strategy.value, "total_documents": 0}
        
        # Convert to DocumentWithScore objects
        scored_docs = self._prepare_documents(documents)
        
        # Select documents that fit in token budget
        selected_docs = self._select_documents_for_budget(scored_docs, available_tokens)
        
        if not selected_docs:
            return [], {"strategy": self.strategy.value, "total_documents": 0}
        
        # Apply placement strategy
        optimized_docs = self._apply_placement_strategy(selected_docs, query)
        
        # Convert back to original format
        result_documents = [doc.document for doc in optimized_docs]
        
        optimization_info = {
            "strategy": self.strategy.value,
            "total_documents": len(result_documents),
            "original_count": len(documents),
            "tokens_used": sum(doc.token_count for doc in optimized_docs),
            "available_tokens": available_tokens,
            "placement_order": [doc.document_id for doc in optimized_docs]
        }
        
        logger.info(f"Context optimization: {optimization_info}")
        
        return result_documents, optimization_info
    
    def _prepare_documents(self, documents: List[Dict[str, Any]]) -> List[DocumentWithScore]:
        """Convert documents to DocumentWithScore objects."""
        scored_docs = []
        
        for i, doc in enumerate(documents):
            # Extract relevance score (from search/rerank)
            score = doc.get("score", 0.0)
            if isinstance(score, (int, float)):
                relevance_score = float(score)
            else:
                relevance_score = 0.0
            
            # Estimate token count (rough approximation)
            content = doc.get("content", "")
            token_count = len(content.split()) * 1.3  # Rough token estimation
            
            # Extract metadata
            doc_info = doc.get("document", {})
            document_id = str(doc_info.get("id", f"doc_{i}"))
            title = doc_info.get("title", "Untitled")
            content_preview = content[:100] + "..." if len(content) > 100 else content
            
            scored_docs.append(DocumentWithScore(
                document=doc,
                relevance_score=relevance_score,
                token_count=int(token_count),
                document_id=document_id,
                title=title,
                content_preview=content_preview
            ))
        
        return scored_docs
    
    def _select_documents_for_budget(
        self, 
        scored_docs: List[DocumentWithScore], 
        available_tokens: int
    ) -> List[DocumentWithScore]:
        """
        Select documents that fit within token budget using smart selection.
        Instead of just taking the first N documents, we optimize for value.
        """
        if not scored_docs:
            return []
        
        # Sort by relevance score (highest first)
        sorted_docs = sorted(scored_docs, key=lambda x: x.relevance_score, reverse=True)
        
        # Greedy selection with value optimization
        selected = []
        used_tokens = 0
        
        # First pass: select highest value documents that fit
        for doc in sorted_docs:
            if used_tokens + doc.token_count <= available_tokens:
                selected.append(doc)
                used_tokens += doc.token_count
            elif not selected:  # If no documents fit, take the first one (truncated)
                # For very large documents, we might need to truncate
                selected.append(doc)
                break
        
        return selected
    
    def _apply_placement_strategy(
        self, 
        selected_docs: List[DocumentWithScore], 
        query: str = ""
    ) -> List[DocumentWithScore]:
        """Apply the selected placement strategy."""
        if len(selected_docs) <= 1:
            return selected_docs
        
        if self.strategy == DocumentPlacementStrategy.RELEVANCE_FIRST:
            return self._relevance_first_placement(selected_docs)
        elif self.strategy == DocumentPlacementStrategy.RELEVANCE_LAST:
            return self._relevance_last_placement(selected_docs)
        elif self.strategy == DocumentPlacementStrategy.SANDWICH:
            return self._sandwich_placement(selected_docs)
        elif self.strategy == DocumentPlacementStrategy.INTERLEAVED:
            return self._interleaved_placement(selected_docs)
        elif self.strategy == DocumentPlacementStrategy.ATTENTION_OPTIMIZED:
            return self._attention_optimized_placement(selected_docs)
        else:
            return selected_docs
    
    def _relevance_first_placement(self, docs: List[DocumentWithScore]) -> List[DocumentWithScore]:
        """Place most relevant documents first (current default behavior)."""
        return sorted(docs, key=lambda x: x.relevance_score, reverse=True)
    
    def _relevance_last_placement(self, docs: List[DocumentWithScore]) -> List[DocumentWithScore]:
        """Place most relevant documents last (leverages recency bias)."""
        return sorted(docs, key=lambda x: x.relevance_score)
    
    def _sandwich_placement(self, docs: List[DocumentWithScore]) -> List[DocumentWithScore]:
        """Place most relevant documents at start and end."""
        sorted_docs = sorted(docs, key=lambda x: x.relevance_score, reverse=True)
        
        if len(sorted_docs) <= 2:
            return sorted_docs
        
        result = []
        high_relevance = []
        low_relevance = []
        
        # Split into high and low relevance
        mid_point = len(sorted_docs) // 2
        high_relevance = sorted_docs[:mid_point]
        low_relevance = sorted_docs[mid_point:]
        
        # Place highest relevance first
        if high_relevance:
            result.append(high_relevance[0])
        
        # Place low relevance in middle
        result.extend(low_relevance)
        
        # Place remaining high relevance at end
        result.extend(high_relevance[1:])
        
        return result
    
    def _interleaved_placement(self, docs: List[DocumentWithScore]) -> List[DocumentWithScore]:
        """Alternate between high and low relevance documents."""
        sorted_docs = sorted(docs, key=lambda x: x.relevance_score, reverse=True)
        
        if len(sorted_docs) <= 2:
            return sorted_docs
        
        result = []
        high_idx = 0
        low_idx = len(sorted_docs) - 1
        
        while high_idx <= low_idx:
            if len(result) % 2 == 0:  # Even positions: high relevance
                result.append(sorted_docs[high_idx])
                high_idx += 1
            else:  # Odd positions: low relevance
                result.append(sorted_docs[low_idx])
                low_idx -= 1
        
        return result
    
    def _attention_optimized_placement(self, docs: List[DocumentWithScore]) -> List[DocumentWithScore]:
        """
        Optimize placement based on attention patterns.
        Places highest relevance documents in positions with highest attention weights.

        Fixed version: Ensures highest relevance documents are prioritized first,
        then optimizes their placement based on attention patterns.
        """
        if len(docs) <= 2:
            return sorted(docs, key=lambda x: x.relevance_score, reverse=True)

        # Sort documents by relevance (highest first)
        sorted_docs = sorted(docs, key=lambda x: x.relevance_score, reverse=True)

        # Calculate position weights based on document count
        position_weights = self._calculate_position_weights(len(docs))

        # Create position-weight pairs and sort by weight (highest first)
        position_weight_pairs = [(i, weight) for i, weight in enumerate(position_weights)]
        position_weight_pairs.sort(key=lambda x: x[1], reverse=True)

        # Create result array
        result = [None] * len(docs)

        # Assign highest relevance documents to highest attention positions
        for doc_idx, doc in enumerate(sorted_docs):
            if doc_idx < len(position_weight_pairs):
                # Get the position with highest remaining attention weight
                best_position = position_weight_pairs[doc_idx][0]
                result[best_position] = doc
            else:
                # Fallback: place remaining documents in remaining positions
                for i in range(len(result)):
                    if result[i] is None:
                        result[i] = doc
                        break

        return [doc for doc in result if doc is not None]
    
    def _calculate_position_weights(self, num_docs: int) -> List[float]:
        """Calculate attention weights for each position."""
        if num_docs <= 1:
            return [1.0]
        
        weights = []
        for i in range(num_docs):
            # Normalize position to 0-1 range
            normalized_pos = i / (num_docs - 1)
            
            if normalized_pos <= 0.2:  # First 20% - high attention
                weight = self.attention_weights["start"]
            elif normalized_pos <= 0.4:  # Next 20% - good attention
                weight = self.attention_weights["early"]
            elif normalized_pos <= 0.6:  # Middle 20% - low attention
                weight = self.attention_weights["middle"]
            elif normalized_pos <= 0.8:  # Next 20% - decent attention
                weight = self.attention_weights["late"]
            else:  # Last 20% - high attention
                weight = self.attention_weights["end"]
            
            weights.append(weight)
        
        return weights
    
    def _find_best_available_position(
        self, 
        preferred_pos: int, 
        used_positions: set, 
        total_positions: int
    ) -> int:
        """Find the best available position close to the preferred position."""
        if preferred_pos not in used_positions:
            return preferred_pos
        
        # Search outward from preferred position
        for offset in range(1, total_positions):
            # Try position before preferred
            pos_before = preferred_pos - offset
            if pos_before >= 0 and pos_before not in used_positions:
                return pos_before
            
            # Try position after preferred
            pos_after = preferred_pos + offset
            if pos_after < total_positions and pos_after not in used_positions:
                return pos_after
        
        # Fallback: find any available position
        for i in range(total_positions):
            if i not in used_positions:
                return i
        
        return 0  # Should never reach here


# Factory function for easy usage
def create_context_optimizer(strategy: str = "attention_optimized", model_name: str = "") -> ContextOptimizer:
    """Create a context optimizer with the specified strategy."""
    try:
        strategy_enum = DocumentPlacementStrategy(strategy)
        return ContextOptimizer(strategy_enum, model_name)
    except ValueError:
        logger.warning(f"Unknown strategy '{strategy}', using attention_optimized")
        return ContextOptimizer(DocumentPlacementStrategy.ATTENTION_OPTIMIZED, model_name)


def create_model_optimized_context_optimizer(model_name: str) -> ContextOptimizer:
    """Create a context optimizer optimized for a specific model."""
    if context_config:
        strategy = context_config.get_strategy_for_model(model_name)
        return create_context_optimizer(strategy, model_name)
    else:
        return create_context_optimizer("attention_optimized", model_name)


# Global instance for easy import
default_context_optimizer = create_context_optimizer()
