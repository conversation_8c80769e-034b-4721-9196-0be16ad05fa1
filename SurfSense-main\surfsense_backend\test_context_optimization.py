#!/usr/bin/env python3
"""
Test script để verify context optimization strategies hoạt động đúng.
"""

import os
import sys
from typing import List, Dict, Any

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.agents.researcher.context_optimizer import ContextOptimizer, DocumentPlacementStrategy
from app.config.context_config import ContextConfig


def create_test_documents(num_docs: int = 5) -> List[Dict[str, Any]]:
    """Tạo test documents với scores khác nhau."""
    documents = []
    for i in range(num_docs):
        # Tạo documents với scores giảm dần (doc 0 có score cao nhất)
        score = 1.0 - (i * 0.2)  # 1.0, 0.8, 0.6, 0.4, 0.2
        doc = {
            "content": f"This is test document {i} with high relevance content about the query topic.",
            "score": score,
            "chunk_id": f"chunk_{i}",
            "document": {
                "id": f"doc_{i}",
                "title": f"Test Document {i}",
                "document_type": "TEST"
            }
        }
        documents.append(doc)
    return documents


def test_strategy(strategy: DocumentPlacementStrategy, documents: List[Dict[str, Any]]) -> None:
    """Test một strategy cụ thể."""
    print(f"\n=== Testing {strategy.value} ===")
    
    # Tạo optimizer với strategy
    optimizer = ContextOptimizer(strategy=strategy, model_name="test-model")
    
    # Optimize documents
    optimized_docs, info = optimizer.optimize_document_order(
        documents=documents,
        available_tokens=10000,  # Đủ lớn để chứa tất cả documents
        query="test query"
    )
    
    print(f"Strategy: {info.get('strategy', 'unknown')}")
    print(f"Total documents: {info.get('total_documents', 0)}")
    print(f"Selected documents: {len(optimized_docs)}")
    
    print("\nDocument order (by position):")
    for i, doc_data in enumerate(optimized_docs):
        doc = doc_data.document if hasattr(doc_data, 'document') else doc_data
        doc_id = doc.get("document", {}).get("id", "unknown")
        score = doc.get("score", 0.0)
        print(f"  Position {i}: {doc_id} (score: {score:.2f})")
    
    # Verify cho relevance_first strategy
    if strategy == DocumentPlacementStrategy.RELEVANCE_FIRST:
        print("\nVerification for RELEVANCE_FIRST:")
        scores = []
        for doc_data in optimized_docs:
            doc = doc_data.document if hasattr(doc_data, 'document') else doc_data
            scores.append(doc.get("score", 0.0))
        
        is_descending = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
        print(f"  ✓ Scores in descending order: {is_descending}")
        
        if len(optimized_docs) > 0:
            first_doc = optimized_docs[0]
            first_doc_data = first_doc.document if hasattr(first_doc, 'document') else first_doc
            first_score = first_doc_data.get("score", 0.0)
            print(f"  ✓ First document has highest score: {first_score:.2f}")


def test_context_config():
    """Test context configuration."""
    print("\n=== Testing Context Configuration ===")
    
    # Test default config
    config = ContextConfig()
    print(f"Default strategy: {config.strategy.value}")
    print(f"Optimization enabled: {config.enabled}")
    print(f"Output buffer: {config.output_buffer_percentage}")
    print(f"Max documents: {config.max_documents}")
    
    # Test environment variable override
    original_strategy = os.environ.get("CONTEXT_OPTIMIZATION_STRATEGY")
    os.environ["CONTEXT_OPTIMIZATION_STRATEGY"] = "relevance_first"
    
    config_with_env = ContextConfig()
    print(f"Strategy with env override: {config_with_env.strategy.value}")
    
    # Restore original environment
    if original_strategy:
        os.environ["CONTEXT_OPTIMIZATION_STRATEGY"] = original_strategy
    else:
        os.environ.pop("CONTEXT_OPTIMIZATION_STRATEGY", None)


def main():
    """Main test function."""
    print("Context Optimization Test Suite")
    print("=" * 50)
    
    # Test context configuration
    test_context_config()
    
    # Create test documents
    test_docs = create_test_documents(5)
    print(f"\nCreated {len(test_docs)} test documents")
    print("Original document scores:")
    for i, doc in enumerate(test_docs):
        print(f"  doc_{i}: {doc['score']:.2f}")
    
    # Test different strategies
    strategies_to_test = [
        DocumentPlacementStrategy.RELEVANCE_FIRST,
        DocumentPlacementStrategy.ATTENTION_OPTIMIZED,
        DocumentPlacementStrategy.RELEVANCE_LAST,
        DocumentPlacementStrategy.SANDWICH
    ]
    
    for strategy in strategies_to_test:
        test_strategy(strategy, test_docs)
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("\nRecommendation: Use RELEVANCE_FIRST strategy to ensure")
    print("highest ranked documents are prioritized first.")


if __name__ == "__main__":
    main()
