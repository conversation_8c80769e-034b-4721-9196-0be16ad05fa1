"""
Vietnamese Response Filter

This module provides utilities to ensure responses contain only Vietnamese text
and no Chinese characters.
"""

import os
import re
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class VietnameseResponseFilter:
    """Filter to ensure responses contain only Vietnamese text."""
    
    def __init__(self):
        # Chinese character ranges (CJK Unified Ideographs)
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]')
        
        # Vietnamese diacritics pattern
        self.vietnamese_diacritics = re.compile(r'[àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđĐ]')
        
        # Common Vietnamese words for validation
        self.vietnamese_indicators = [
            'tôi', 'bạn', 'của', 'và', 'với', 'trong', 'trên', 'dưới', 'theo', 'để',
            'có', 'là', 'được', 'sẽ', 'đã', 'đang', 'từ', 'về', 'cho', 'nh<PERSON>',
            'này', 'đó', 'những', 'các', 'một', 'hai', 'ba', 'nhiều', 'ít', 'rất'
        ]
    
    def contains_chinese_characters(self, text: str) -> bool:
        """
        Check if text contains Chinese characters.
        
        Args:
            text: Text to check
            
        Returns:
            True if Chinese characters are found
        """
        return bool(self.chinese_pattern.search(text))
    
    def extract_chinese_characters(self, text: str) -> list:
        """
        Extract all Chinese characters from text.
        
        Args:
            text: Text to analyze
            
        Returns:
            List of Chinese characters found
        """
        return self.chinese_pattern.findall(text)
    
    def is_likely_vietnamese(self, text: str) -> bool:
        """
        Check if text is likely Vietnamese based on common indicators.
        
        Args:
            text: Text to check
            
        Returns:
            True if text appears to be Vietnamese
        """
        text_lower = text.lower()
        
        # Check for Vietnamese diacritics
        has_diacritics = bool(self.vietnamese_diacritics.search(text))
        
        # Check for common Vietnamese words
        vietnamese_word_count = sum(1 for word in self.vietnamese_indicators if word in text_lower)
        
        # Consider it Vietnamese if it has diacritics OR multiple Vietnamese words
        return has_diacritics or vietnamese_word_count >= 2
    
    def filter_response(self, response: str) -> tuple[str, bool]:
        """
        Filter response to ensure it's Vietnamese only.
        
        Args:
            response: Original response text
            
        Returns:
            Tuple of (filtered_response, was_modified)
        """
        if not response:
            return response, False
        
        # Check for Chinese characters
        chinese_chars = self.extract_chinese_characters(response)
        
        if not chinese_chars:
            # No Chinese characters found, check if it's Vietnamese
            if self.is_likely_vietnamese(response):
                return response, False
            else:
                # Not clearly Vietnamese, add warning
                warning_msg = "\n\n⚠️ Lưu ý: Phản hồi này có thể không hoàn toàn bằng tiếng Việt. Vui lòng thử lại câu hỏi để nhận được phản hồi tiếng Việt tốt hơn."
                return response + warning_msg, True
        
        # Chinese characters found - log and provide Vietnamese replacement
        logger.warning(f"Chinese characters detected in response: {chinese_chars}")
        
        replacement_response = """Xin lỗi, tôi phát hiện phản hồi trước đó có chứa ký tự Trung Quốc. Đây là phản hồi bằng tiếng Việt:

Tôi hiểu câu hỏi của bạn và sẽ cố gắng trả lời bằng tiếng Việt. Tuy nhiên, do có vấn đề kỹ thuật, tôi không thể cung cấp phản hồi chi tiết ngay lúc này. 

Vui lòng thử lại câu hỏi của bạn để tôi có thể đưa ra câu trả lời hoàn chỉnh bằng tiếng Việt."""
        
        return replacement_response, True
    
    def validate_vietnamese_response(self, response: str) -> dict:
        """
        Comprehensive validation of Vietnamese response.
        
        Args:
            response: Response to validate
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'is_valid': True,
            'has_chinese': False,
            'is_vietnamese': False,
            'chinese_characters': [],
            'warnings': []
        }
        
        if not response:
            result['warnings'].append('Empty response')
            result['is_valid'] = False
            return result
        
        # Check for Chinese characters
        chinese_chars = self.extract_chinese_characters(response)
        if chinese_chars:
            result['has_chinese'] = True
            result['chinese_characters'] = chinese_chars
            result['is_valid'] = False
            result['warnings'].append(f'Contains Chinese characters: {", ".join(set(chinese_chars))}')
        
        # Check if Vietnamese
        result['is_vietnamese'] = self.is_likely_vietnamese(response)
        if not result['is_vietnamese']:
            result['warnings'].append('Does not appear to be Vietnamese text')
        
        return result


# Global instance for easy import
vietnamese_filter = VietnameseResponseFilter()


def ensure_vietnamese_response(response: str) -> str:
    """
    Convenience function to ensure response is Vietnamese only.

    Args:
        response: Original response

    Returns:
        Filtered Vietnamese response
    """
    # Check if Vietnamese enforcement is enabled
    enforce_vietnamese = os.getenv("ENFORCE_VIETNAMESE_ONLY", "true").lower() == "true"

    if not enforce_vietnamese:
        return response

    filtered_response, was_modified = vietnamese_filter.filter_response(response)

    if was_modified:
        logger.info("Response was modified to ensure Vietnamese language compliance")

    return filtered_response
