#!/usr/bin/env python3
"""
Test script to verify Vietnamese language enforcement in QnA prompts.
This script tests the prompt generation to ensure it properly enforces Vietnamese language.
"""

import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.researcher.qna_agent.prompts import (
    get_qna_citation_system_prompt,
    get_qna_no_documents_system_prompt
)
from app.utils.vietnamese_response_filter import vietnamese_filter, ensure_vietnamese_response

def test_vietnamese_language_enforcement():
    """Test that prompts contain proper Vietnamese language enforcement."""
    
    print("Testing QnA Citation System Prompt...")
    citation_prompt = get_qna_citation_system_prompt()
    
    # Check for Vietnamese language enforcement
    vietnamese_checks = [
        "ALWAYS respond in Vietnamese language",
        "NEVER use Chinese characters",
        "中文/汉字",
        "Latin alphabet and Vietnamese diacritics",
        "If you accidentally start writing in Chinese, immediately stop"
    ]
    
    print("\nChecking Citation Prompt for Vietnamese enforcement:")
    for check in vietnamese_checks:
        if check in citation_prompt:
            print(f"✓ Found: '{check}'")
        else:
            print(f"✗ Missing: '{check}'")
    
    print("\n" + "="*60)
    print("Testing QnA No Documents System Prompt...")
    no_docs_prompt = get_qna_no_documents_system_prompt()
    
    print("\nChecking No Documents Prompt for Vietnamese enforcement:")
    for check in vietnamese_checks:
        if check in no_docs_prompt:
            print(f"✓ Found: '{check}'")
        else:
            print(f"✗ Missing: '{check}'")
    
    print("\n" + "="*60)
    print("Sample of Citation Prompt (first 500 chars):")
    print(citation_prompt[:500] + "...")
    
    print("\n" + "="*60)
    print("Sample of No Documents Prompt (first 500 chars):")
    print(no_docs_prompt[:500] + "...")

def test_vietnamese_filter():
    """Test the Vietnamese response filter."""
    print("\n" + "="*60)
    print("Testing Vietnamese Response Filter...")

    # Test cases
    test_cases = [
        ("Xin chào, tôi có thể giúp bạn.", "Pure Vietnamese"),
        ("Hello, 你好, Xin chào", "Mixed languages with Chinese"),
        ("根据文档，我理解这个问题", "Pure Chinese"),
        ("Dựa trên tài liệu, tôi hiểu vấn đề này", "Vietnamese with diacritics"),
        ("This is English text", "Pure English"),
        ("", "Empty string")
    ]

    for text, description in test_cases:
        print(f"\nTesting: {description}")
        print(f"Input: '{text}'")

        # Test Chinese detection
        has_chinese = vietnamese_filter.contains_chinese_characters(text)
        print(f"Contains Chinese: {has_chinese}")

        if has_chinese:
            chinese_chars = vietnamese_filter.extract_chinese_characters(text)
            print(f"Chinese characters found: {chinese_chars}")

        # Test Vietnamese detection
        is_vietnamese = vietnamese_filter.is_likely_vietnamese(text)
        print(f"Is Vietnamese: {is_vietnamese}")

        # Test filtering
        filtered, was_modified = vietnamese_filter.filter_response(text)
        print(f"Was modified: {was_modified}")
        if was_modified:
            print(f"Filtered output: '{filtered[:100]}...'")

if __name__ == "__main__":
    test_vietnamese_language_enforcement()
    test_vietnamese_filter()
