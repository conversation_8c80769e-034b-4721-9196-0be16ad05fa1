# Cưỡng chế Ngôn ngữ Tiếng Việt trong SurfSense

## Tổng quan

Tài liệu này mô tả các biện pháp được thực hiện để đảm bảo hệ thống QnA của SurfSense chỉ tạo ra phản hồi bằng tiếng Việt và hoàn toàn ngăn chặn việc tạo ra chữ Trung Quốc (Hán tự).

## Vấn đề

Trước đây, hệ thống QnA đôi khi tạo ra phản hồi chứa chữ Trung Quốc thay vì tiếng Việt, gây khó khăn cho người dùng Việt Nam.

## Giải pháp

### 1. Cải thiện System Prompts

**File được cập nhật:** `app/agents/researcher/qna_agent/prompts.py`

- Thêm cảnh báo mạnh mẽ về việc cấm sử dụng chữ Trung Quốc
- Sử dụng emoji 🚨 để làm nổi bật các yêu cầu quan trọng
- Cung cấp ví dụ cụ thể về tiếng Việt đúng và chữ Trung Quốc bị cấm
- Thêm chỉ dẫn về việc dừng ngay lập tức nếu phát hiện đang viết chữ Trung Quốc

**Các thay đổi chính:**
```
🚨 CRITICAL LANGUAGE ENFORCEMENT 🚨
YOU ARE ABSOLUTELY FORBIDDEN FROM USING CHINESE CHARACTERS OR WRITING IN CHINESE.
YOU MUST ONLY RESPOND IN VIETNAMESE USING LATIN ALPHABET WITH VIETNAMESE DIACRITICS.
ANY RESPONSE CONTAINING CHINESE CHARACTERS WILL BE COMPLETELY REJECTED.
```

### 2. Tăng cường Human Message Instructions

**File được cập nhật:** `app/agents/researcher/qna_agent/nodes.py`

- Thêm chỉ dẫn chi tiết về ngôn ngữ trong human message
- Cung cấp ví dụ cụ thể về tiếng Việt và chữ Trung Quốc bị cấm
- Sử dụng định dạng cảnh báo mạnh mẽ với emoji

### 3. Vietnamese Response Filter

**File mới:** `app/utils/vietnamese_response_filter.py`

Tạo một lớp bảo vệ cuối cùng để:
- Phát hiện chữ Trung Quốc trong phản hồi
- Kiểm tra xem văn bản có phải tiếng Việt không
- Thay thế phản hồi chứa chữ Trung Quốc bằng thông báo tiếng Việt
- Ghi log các trường hợp vi phạm

**Tính năng chính:**
- `contains_chinese_characters()`: Phát hiện chữ Trung Quốc
- `is_likely_vietnamese()`: Kiểm tra tiếng Việt
- `filter_response()`: Lọc và thay thế phản hồi không hợp lệ
- `ensure_vietnamese_response()`: Hàm tiện ích chính

### 4. Tích hợp Filter vào QnA Agent

**File được cập nhật:** `app/agents/researcher/qna_agent/nodes.py`

- Import Vietnamese response filter
- Áp dụng filter cho tất cả phản hồi từ LLM
- Đảm bảo phản hồi cuối cùng luôn tuân thủ yêu cầu tiếng Việt

### 5. Cấu hình Language Settings cho Document Processing

**File được cập nhật:** `app/routes/documents_routes.py`

- Thay đổi LlamaParse từ `language="en"` thành `language="vi"`
- Thay đổi UnstructuredLoader từ `languages=["eng"]` thành `languages=["vie"]`
- Đảm bảo xử lý tài liệu được tối ưu cho tiếng Việt

### 6. Environment Configuration

**File được cập nhật:** `.env.example`

Thêm các biến môi trường:
```
ENABLE_VIETNAMESE_OPTIMIZATION=true
ENFORCE_VIETNAMESE_ONLY=true
```

## Cách sử dụng

### 1. Cấu hình Environment

Thêm vào file `.env`:
```
ENABLE_VIETNAMESE_OPTIMIZATION=true
ENFORCE_VIETNAMESE_ONLY=true
```

### 2. Kiểm tra hoạt động

Chạy test script:
```bash
cd surfsense_backend
python test_vietnamese_qna.py
```

### 3. Monitoring

Filter sẽ tự động ghi log khi phát hiện và xử lý chữ Trung Quốc:
```
WARNING: Chinese characters detected in response: ['你', '好']
```

## Các lớp bảo vệ

1. **System Prompt Level**: Chỉ dẫn mạnh mẽ trong system prompt
2. **Human Message Level**: Cảnh báo trong human message
3. **Response Filter Level**: Kiểm tra và thay thế phản hồi
4. **Document Processing Level**: Tối ưu xử lý tài liệu tiếng Việt

## Kết quả mong đợi

- **100% phản hồi tiếng Việt**: Không còn chữ Trung Quốc trong phản hồi
- **Tự động phát hiện**: Hệ thống tự động phát hiện và xử lý vi phạm
- **Thông báo rõ ràng**: Người dùng được thông báo khi có vấn đề
- **Logging đầy đủ**: Ghi log để theo dõi và debug

## Troubleshooting

### Nếu vẫn thấy chữ Trung Quốc:

1. Kiểm tra environment variables
2. Xem log để tìm nguyên nhân
3. Kiểm tra LLM configuration
4. Thử restart service

### Debug:

```python
from app.utils.vietnamese_response_filter import vietnamese_filter

# Kiểm tra text
result = vietnamese_filter.validate_vietnamese_response("your_text_here")
print(result)
```

## Lưu ý

- Filter chỉ hoạt động khi `ENFORCE_VIETNAMESE_ONLY=true`
- Hệ thống sẽ thay thế toàn bộ phản hồi nếu phát hiện chữ Trung Quốc
- Các thay đổi không ảnh hưởng đến hiệu suất hệ thống
- Filter có thể được tùy chỉnh thêm nếu cần
