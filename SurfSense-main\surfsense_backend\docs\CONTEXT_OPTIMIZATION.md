# Context Optimization Strategies

Hệ thống SurfSense hỗ trợ nhiều chiến lược tối ưu hóa context để giải quyết vấn đề "Lost in the Middle" và đảm bảo tài liệu được sắp xếp theo thứ tự ưu tiên phù hợp.

## Các Strategy Có Sẵn

### 1. `relevance_first` (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị cho hầu hết trường hợp)
- **<PERSON><PERSON> tả**: Sắp xếp tài liệu theo thứ tự relevance score từ cao đến thấp
- **Ưu điểm**: 
  - <PERSON><PERSON><PERSON>, dễ hiểu
  - Đảm bảo tài liệu quan trọng nhất được đặt đầu tiên
  - Phù hợp với expectation của người dùng
- **Nhược điểm**: <PERSON><PERSON> thể gặp vấn đề "Lost in the Middle" với context dài
- **<PERSON><PERSON> nào sử dụng**: <PERSON><PERSON> cần đảm bảo tài liệu có ranking cao được ưu tiên tuyệt đối

### 2. `attention_optimized` (Mặc định cũ)
- **Mô tả**: Tối ưu hóa vị trí tài liệu dựa trên attention patterns của LLM
- **Ưu điểm**: 
  - Giải quyết vấn đề "Lost in the Middle"
  - Tối ưu hóa hiệu suất với context dài
- **Nhược điểm**: 
  - Có thể đặt tài liệu quan trọng ở vị trí không phải đầu tiên
  - Logic phức tạp hơn
- **Khi nào sử dụng**: Khi có nhiều tài liệu và cần tối ưu hóa attention

### 3. `relevance_last`
- **Mô tả**: Đặt tài liệu quan trọng nhất ở cuối (tận dụng recency bias)
- **Ưu điểm**: Tận dụng xu hướng model chú ý nhiều hơn đến thông tin gần đây
- **Nhược điểm**: Trái ngược với expectation thông thường
- **Khi nào sử dụng**: Khi model có recency bias mạnh

### 4. `sandwich`
- **Mô tả**: Đặt tài liệu quan trọng nhất ở đầu và cuối
- **Ưu điểm**: Tận dụng cả primacy và recency effect
- **Nhược điểm**: Phức tạp, có thể gây nhầm lẫn
- **Khi nào sử dụng**: Khi có ít tài liệu và cần tối ưu hóa attention

### 5. `interleaved`
- **Mô tả**: Xen kẽ tài liệu có relevance cao và thấp
- **Ưu điểm**: Phân bố đều attention
- **Nhược điểm**: Có thể làm giảm coherence
- **Khi nào sử dụng**: Khi cần đảm bảo tất cả tài liệu được chú ý

## Cấu Hình

### Environment Variables

```bash
# Strategy chính (khuyến nghị: relevance_first)
CONTEXT_OPTIMIZATION_STRATEGY=relevance_first

# Bật/tắt context optimization
ENABLE_CONTEXT_OPTIMIZATION=true

# Phần trăm context window dành cho output
OUTPUT_BUFFER_PERCENTAGE=0.2

# Số token tối thiểu cho documents
MIN_DOCUMENT_TOKENS=500

# Số lượng tài liệu tối đa
MAX_DOCUMENTS_IN_CONTEXT=20

# Debug mode
CONTEXT_OPTIMIZATION_DEBUG=false

# Attention weights (chỉ dùng cho attention_optimized)
ATTENTION_WEIGHT_START=1.0
ATTENTION_WEIGHT_EARLY=0.8
ATTENTION_WEIGHT_MIDDLE=0.4
ATTENTION_WEIGHT_LATE=0.7
ATTENTION_WEIGHT_END=0.9
```

## Khuyến Nghị Sử Dụng

### Cho QnA Agent
- **Khuyến nghị**: `relevance_first`
- **Lý do**: Người dùng mong đợi tài liệu quan trọng nhất được sử dụng đầu tiên

### Cho Research Agent
- **Khuyến nghị**: `attention_optimized` hoặc `sandwich`
- **Lý do**: Cần tối ưu hóa với nhiều tài liệu và context dài

### Cho Sub-section Writer
- **Khuyến nghị**: `relevance_first`
- **Lý do**: Cần đảm bảo thông tin chính xác và có thứ tự ưu tiên rõ ràng

## Troubleshooting

### Vấn đề: Tài liệu quan trọng không được sử dụng đầu tiên
- **Giải pháp**: Đặt `CONTEXT_OPTIMIZATION_STRATEGY=relevance_first`

### Vấn đề: Model bỏ qua tài liệu ở giữa context
- **Giải pháp**: Sử dụng `attention_optimized` hoặc `sandwich`

### Vấn đề: Performance kém với context dài
- **Giải pháp**: Giảm `MAX_DOCUMENTS_IN_CONTEXT` hoặc tăng `MIN_DOCUMENT_TOKENS`

## Monitoring và Debug

Bật debug mode để xem thông tin chi tiết về document placement:

```bash
CONTEXT_OPTIMIZATION_DEBUG=true
```

Khi debug mode được bật, hệ thống sẽ log:
- Strategy được sử dụng
- Số lượng tài liệu trước và sau optimization
- Thứ tự tài liệu cuối cùng
- Token usage statistics
